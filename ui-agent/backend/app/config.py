"""
配置文件
"""

from pydantic_settings import BaseSettings
from pathlib import Path
import os

class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    app_name: str = "智能UI自动化测试平台"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 数据库配置
    database_url: str = "sqlite:///./ui_agent.db"
    
    # AI配置
    ai_execute_model: str = "gpt-4-vision-preview"
    ai_execute_api_base: str = "https://api.openai.com/v1"
    ai_execute_temperature: float = 0.1
    openai_api_key: str = ""
    
    # 文件路径配置
    project_root: Path = Path(__file__).parent.parent.parent
    output_dir: Path = project_root / "output"
    logs_dir: Path = project_root / "logs"
    prompt_dir: Path = project_root / "prompt"
    
    # 自动化配置
    max_scroll_times: int = 3
    screenshot_timeout: int = 30
    operation_timeout: int = 60
    
    # Android配置
    adb_timeout: int = 30
    device_connect_timeout: int = 60
    
    # Web配置
    browser_timeout: int = 30
    page_load_timeout: int = 30
    
    # 语音配置
    speech_recognition_timeout: int = 10
    speech_language: str = "zh-CN"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 创建必要的目录
        self.output_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)

# 全局配置实例
settings = Settings()

# 环境变量配置
def get_env_value(key: str, default: str = "") -> str:
    """获取环境变量值"""
    return os.getenv(key, default)

# AI配置类
class AIConfig:
    """AI相关配置"""
    
    @staticmethod
    def get_openai_config():
        """获取OpenAI配置"""
        return {
            "api_key": get_env_value("OPENAI_API_KEY", settings.openai_api_key),
            "api_base": get_env_value("OPENAI_API_BASE", settings.ai_execute_api_base),
            "model": get_env_value("AI_MODEL", settings.ai_execute_model),
            "temperature": float(get_env_value("AI_TEMPERATURE", str(settings.ai_execute_temperature)))
        }
    
    @staticmethod
    def get_prompt_template(template_name: str) -> str:
        """获取提示词模板"""
        template_path = settings.prompt_dir / f"{template_name}.md"
        if template_path.exists():
            return template_path.read_text(encoding="utf-8")
        return ""

# 数据库配置
class DatabaseConfig:
    """数据库配置"""
    
    @staticmethod
    def get_database_url():
        """获取数据库URL"""
        return get_env_value("DATABASE_URL", settings.database_url)

# 日志配置
class LogConfig:
    """日志配置"""
    
    @staticmethod
    def get_log_config():
        """获取日志配置"""
        return {
            "level": get_env_value("LOG_LEVEL", "INFO"),
            "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            "rotation": "1 day",
            "retention": "30 days",
            "compression": "zip"
        }
