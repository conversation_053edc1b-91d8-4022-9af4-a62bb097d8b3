"""
智能UI自动化测试平台 - FastAPI主应用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import os
from pathlib import Path

from app.routers import automation, devices, browser, tasks, reports
from app.database import engine, Base
from app.config import settings

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 创建FastAPI应用
app = FastAPI(
    title="智能UI自动化测试平台",
    description="基于AI的智能UI自动化测试平台API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
static_dir = Path(__file__).parent.parent.parent / "output"
static_dir.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# 注册路由
app.include_router(automation.router, prefix="/api", tags=["自动化执行"])
app.include_router(devices.router, prefix="/api", tags=["设备管理"])
app.include_router(browser.router, prefix="/api", tags=["浏览器管理"])
app.include_router(tasks.router, prefix="/api", tags=["任务管理"])
app.include_router(reports.router, prefix="/api", tags=["报告管理"])

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径，返回欢迎页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>智能UI自动化测试平台</title>
        <meta charset="utf-8">
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 40px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                text-align: center;
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            }
            h1 { 
                font-size: 3em; 
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            p { 
                font-size: 1.2em; 
                margin-bottom: 30px;
                opacity: 0.9;
            }
            .links {
                display: flex;
                gap: 20px;
                justify-content: center;
                flex-wrap: wrap;
            }
            .link {
                background: rgba(255,255,255,0.2);
                padding: 15px 30px;
                border-radius: 10px;
                text-decoration: none;
                color: white;
                transition: all 0.3s ease;
                border: 1px solid rgba(255,255,255,0.3);
            }
            .link:hover {
                background: rgba(255,255,255,0.3);
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            }
            .status {
                margin-top: 30px;
                padding: 15px;
                background: rgba(0,255,0,0.2);
                border-radius: 10px;
                border: 1px solid rgba(0,255,0,0.3);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 智能UI自动化测试平台</h1>
            <p>基于AI的智能UI自动化测试平台，支持Android和Web端自动化操作</p>
            
            <div class="links">
                <a href="/docs" class="link">📚 API文档</a>
                <a href="/redoc" class="link">📖 ReDoc文档</a>
                <a href="http://localhost:3000" class="link">🖥️ 前端界面</a>
            </div>
            
            <div class="status">
                <strong>🟢 服务状态：运行中</strong><br>
                后端API服务已启动，可以开始使用！
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "message": "智能UI自动化测试平台运行正常",
        "version": "1.0.0"
    }

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    print("🚀 智能UI自动化测试平台启动成功")
    print(f"📱 前端地址: http://localhost:3000")
    print(f"🔧 后端API: http://localhost:8000")
    print(f"📚 API文档: http://localhost:8000/docs")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    print("🛑 智能UI自动化测试平台已关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
