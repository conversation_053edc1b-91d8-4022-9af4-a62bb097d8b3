#!/usr/bin/env python3
"""
智能UI自动化测试平台启动脚本
支持Mac和Windows系统一键启动前后端服务
"""

import os
import sys
import subprocess
import time
import platform
import signal
import threading
from pathlib import Path

class UIAgentLauncher:
    def __init__(self):
        self.system = platform.system()
        self.project_root = Path(__file__).parent
        self.backend_process = None
        self.frontend_process = None
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("🔍 检查依赖...")
        
        # 检查Python
        try:
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
                print("❌ Python版本需要3.8+")
                return False
            print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        except Exception as e:
            print(f"❌ Python检查失败: {e}")
            return False
            
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装")
                return False
        except FileNotFoundError:
            print("❌ Node.js未安装")
            return False
            
        return True
    
    def install_python_dependencies(self):
        """安装Python依赖"""
        print("📦 安装Python依赖...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                         check=True, cwd=self.project_root)
            print("✅ Python依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Python依赖安装失败: {e}")
            return False
    
    def install_frontend_dependencies(self):
        """安装前端依赖"""
        frontend_dir = self.project_root / 'frontend'
        if not frontend_dir.exists():
            print("⚠️ 前端目录不存在，跳过前端依赖安装")
            return True
            
        print("📦 安装前端依赖...")
        try:
            subprocess.run(['npm', 'install'], check=True, cwd=frontend_dir)
            print("✅ 前端依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 前端依赖安装失败: {e}")
            return False
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        backend_dir = self.project_root / 'backend'
        
        try:
            if self.system == "Windows":
                self.backend_process = subprocess.Popen(
                    [sys.executable, '-m', 'uvicorn', 'app.main:app', '--host', '0.0.0.0', '--port', '8000', '--reload'],
                    cwd=backend_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                self.backend_process = subprocess.Popen(
                    [sys.executable, '-m', 'uvicorn', 'app.main:app', '--host', '0.0.0.0', '--port', '8000', '--reload'],
                    cwd=backend_dir,
                    preexec_fn=os.setsid
                )
            
            print("✅ 后端服务启动成功 (http://localhost:8000)")
            return True
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        frontend_dir = self.project_root / 'frontend'
        if not frontend_dir.exists():
            print("⚠️ 前端目录不存在，跳过前端启动")
            return True
            
        print("🚀 启动前端服务...")
        
        try:
            if self.system == "Windows":
                self.frontend_process = subprocess.Popen(
                    ['npm', 'run', 'dev'],
                    cwd=frontend_dir,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                self.frontend_process = subprocess.Popen(
                    ['npm', 'run', 'dev'],
                    cwd=frontend_dir,
                    preexec_fn=os.setsid
                )
            
            print("✅ 前端服务启动成功 (http://localhost:3000)")
            return True
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return False
    
    def stop_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止服务...")
        
        if self.backend_process:
            try:
                if self.system == "Windows":
                    self.backend_process.terminate()
                else:
                    os.killpg(os.getpgid(self.backend_process.pid), signal.SIGTERM)
                print("✅ 后端服务已停止")
            except Exception as e:
                print(f"⚠️ 停止后端服务时出错: {e}")
        
        if self.frontend_process:
            try:
                if self.system == "Windows":
                    self.frontend_process.terminate()
                else:
                    os.killpg(os.getpgid(self.frontend_process.pid), signal.SIGTERM)
                print("✅ 前端服务已停止")
            except Exception as e:
                print(f"⚠️ 停止前端服务时出错: {e}")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.stop_services()
        sys.exit(0)
    
    def run(self):
        """主运行函数"""
        print("🎯 智能UI自动化测试平台启动器")
        print("=" * 50)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        if self.system != "Windows":
            signal.signal(signal.SIGTERM, self.signal_handler)
        
        # 检查依赖
        if not self.check_dependencies():
            print("❌ 依赖检查失败，请安装必要的依赖")
            return False
        
        # 安装依赖
        if not self.install_python_dependencies():
            return False
        
        if not self.install_frontend_dependencies():
            return False
        
        # 启动服务
        if not self.start_backend():
            return False
        
        # 等待后端启动
        time.sleep(3)
        
        if not self.start_frontend():
            return False
        
        print("\n" + "=" * 50)
        print("🎉 所有服务启动成功！")
        print("📱 前端地址: http://localhost:3000")
        print("🔧 后端API: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("=" * 50)
        print("按 Ctrl+C 停止所有服务")
        
        try:
            # 保持主进程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_services()
        
        return True

if __name__ == "__main__":
    launcher = UIAgentLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)
