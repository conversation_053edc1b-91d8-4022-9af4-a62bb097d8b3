# 智能UI自动化测试平台

## 项目简介

这是一个基于AI的智能UI自动化测试平台，支持Android端和Web端的自动化操作。通过自然语言或语音输入任务描述，系统会调用大模型进行智能规划，自动执行UI操作并生成详细的测试报告。

## 技术栈

### 前端
- Vue.js 3 + TypeScript
- Element Plus UI组件库
- Vite构建工具
- Axios HTTP客户端

### 后端
- Python 3.8+
- FastAPI Web框架
- SQLite数据库
- Pydantic数据验证

### 自动化框架
- **Android端**: airtest + poco + python
- **Web端**: playwright + python

## 功能特性

### 核心功能
1. **多端支持**: 支持Android设备和Web浏览器自动化
2. **智能规划**: 基于大模型的任务智能分解和执行
3. **多种输入**: 支持文本输入和语音输入任务
4. **实时监控**: 实时截图和执行状态展示
5. **详细报告**: 生成包含截图、日志、DOM信息的HTML报告
6. **历史记录**: 完整的任务执行历史和日志管理

### Android端特有功能
- 多设备切换支持
- APK远程下载安装
- ADB命令执行
- 设备状态检测

### Web端特有功能
- 自定义Cookie和Header设置
- H5/PC页面模式切换
- JavaScript代码执行
- 浏览器实例管理

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- npm或yarn

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ui-agent
```

2. **安装依赖**
```bash
# 安装后端依赖
pip install -r requirements.txt

# 安装前端依赖
cd frontend
npm install
cd ..
```

3. **启动服务**
```bash
# 一键启动前后端服务
python start.py
```

4. **访问应用**
- 前端地址: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 使用说明

1. **连接服务器**
   - 打开前端页面
   - 输入后端服务器地址（默认: localhost:8000）
   - 点击连接按钮

2. **选择自动化类型**
   - Android端: 需要连接Android设备
   - Web端: 需要配置浏览器参数

3. **配置参数**
   - Android: 选择设备、安装应用等
   - Web: 设置URL、Cookie、Header等

4. **执行任务**
   - 输入任务描述或使用语音输入
   - 点击开始执行
   - 查看实时执行过程和结果

## 项目结构

```
ui-agent/
├── backend/                 # 后端代码
│   ├── app/                # FastAPI应用
│   ├── models/             # 数据模型
│   ├── services/           # 业务逻辑
│   └── utils/              # 工具函数
├── frontend/               # 前端代码
│   ├── src/               # Vue源码
│   ├── public/            # 静态资源
│   └── dist/              # 构建输出
├── prompt/                # AI提示词
├── output/                # 输出文件
├── logs/                  # 日志文件
├── requirements.txt       # Python依赖
├── start.py              # 启动脚本
└── README.md             # 项目说明
```

## API文档

### 核心接口

#### 1. 任务执行接口
```
POST /api/process_complex_ai_exec
```
参数:
- `automationType`: 自动化类型 (android/web)
- `bdd_script`: 任务描述
- `last_result`: 上次执行结果
- `last_step_result`: 上次步骤结果
- `max_scroll_times`: 最大滚动次数

#### 2. 设备管理接口
```
GET /api/devices          # 获取设备列表
POST /api/install_apk     # 安装APK
POST /api/adb_command     # 执行ADB命令
```

#### 3. 浏览器管理接口
```
POST /api/browser/init    # 初始化浏览器
POST /api/browser/goto    # 访问页面
POST /api/browser/reset   # 重置浏览器
POST /api/browser/exec_js # 执行JavaScript
```

## 开发指南

### 添加新功能
1. 后端: 在`backend/app/routers/`添加新的路由
2. 前端: 在`frontend/src/components/`添加新组件
3. 数据库: 在`backend/models/`定义新模型

### 调试模式
```bash
# 后端调试
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 前端调试
cd frontend
npm run dev
```

## 部署说明

### 开发环境
使用`python start.py`一键启动

### 生产环境
1. 构建前端: `cd frontend && npm run build`
2. 启动后端: `uvicorn backend.app.main:app --host 0.0.0.0 --port 8000`

### 打包为可执行文件
```bash
# 安装PyInstaller
pip install pyinstaller

# 打包
python build.py
```

## 常见问题

### Q: Android设备连接失败？
A: 确保设备已开启USB调试，并且ADB驱动正常

### Q: Web浏览器启动失败？
A: 检查playwright浏览器是否正确安装: `playwright install`

### Q: 大模型接口调用失败？
A: 检查API密钥配置和网络连接

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题请提交Issue或联系开发团队。
